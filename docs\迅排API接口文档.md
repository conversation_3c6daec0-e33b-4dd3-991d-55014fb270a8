# 迅排设计完整API接口文档

## 概述

迅排设计是一个功能强大的在线创意图片编辑器和海报设计器，本文档描述了完整的API接口，包括基础设计功能、动态参数模板系统、截图服务、文件管理等，适用于第三方系统集成。

## 基础信息

- **基础URL**: `http://localhost:7001` (开发环境)
- **API版本**: v1.0.0
- **认证方式**: Bear<PERSON> (Authorization Header)
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": {
    "reason": "详细错误原因",
    "timestamp": "2025-01-16T10:00:00Z"
  }
}
```

## 1. 模板管理 API

### 1.1 获取模板列表
- **接口**: `GET /api/templates`
- **功能**: 获取模板列表，支持分页和筛选
- **参数**:
  - `page` (可选): 页码，默认1
  - `pageSize` (可选): 每页数量，默认10，最大100
  - `category` (可选): 分类筛选
  - `keyword` (可选): 关键词搜索
  - `type` (可选): 类型筛选，0=模板，1=组件

**请求示例**:
```bash
GET /api/templates?page=1&pageSize=10&category=poster&keyword=日签
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "2",
        "title": "示例模板 - 日签插画手机海报",
        "description": "这是一个精美的设计模板",
        "thumbnail": "http://localhost:7001/static/2-cover.jpg",
        "category": "poster",
        "tags": ["日签", "插画"],
        "width": 1242,
        "height": 2208,
        "textElementsCount": 6,
        "createdAt": "2025-01-16T10:00:00Z",
        "updatedAt": "2025-01-16T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 1.2 获取模板详情
- **接口**: `GET /api/template`
- **功能**: 获取指定模板的详细信息和设计数据
- **参数**:
  - `id` (必填): 模板ID
  - `type` (可选): 类型，0=模板，1=组件

**请求示例**:
```bash
GET /api/template?id=2
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "2",
    "title": "示例模板 - 日签插画手机海报",
    "description": "这是一个精美的设计模板",
    "thumbnail": "http://localhost:7001/static/2-cover.jpg",
    "category": "poster",
    "tags": ["日签", "插画"],
    "width": 1242,
    "height": 2208,
    "textElementsCount": 6,
    "createdAt": "2025-01-16T10:00:00Z",
    "updatedAt": "2025-01-16T10:00:00Z",
    "data": {
      // 完整的模板设计数据JSON
    },
    "metadata": {
      "author": "设计师",
      "version": "1.0",
      "license": "免费商用"
    }
  }
}
```

### 1.3 保存模板
- **接口**: `POST /api/template/save`
- **功能**: 保存或更新模板数据
- **请求体**:
  - `id` (可选): 模板ID，不传则新增
  - `title` (必填): 模板标题
  - `description` (可选): 模板描述
  - `data` (必填): 模板设计数据
  - `width` (必填): 画布宽度
  - `height` (必填): 画布高度
  - `category` (可选): 分类
  - `tags` (可选): 标签数组
  - `type` (可选): 类型，0=模板，1=组件

**请求示例**:
```bash
POST /api/template/save
Content-Type: application/json

{
  "title": "新海报模板",
  "description": "这是一个新的海报模板",
  "width": 750,
  "height": 1334,
  "category": "poster",
  "tags": ["商务", "简约"],
  "data": {
    // 模板设计数据
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "template_new_001",
    "message": "模板保存成功"
  }
}
```

### 1.4 获取模板预览
- **接口**: `GET /api/template/preview`
- **功能**: 生成模板预览图片
- **参数**:
  - `id` (必填): 模板ID
  - `width` (可选): 预览宽度，默认400
  - `height` (可选): 预览高度，默认600
  - `quality` (可选): 图片质量，0.1-1.0，默认0.8

**请求示例**:
```bash
GET /api/template/preview?id=2&width=400&height=600&quality=0.8
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "previewUrl": "http://localhost:7001/static/previews/2_400x600.jpg",
    "width": 400,
    "height": 600,
    "fileSize": 45678,
    "generatedAt": "2025-01-16T10:00:00Z"
  }
}
```

## 2. 动态参数模板系统 API

### 2.1 解析模板参数
- **接口**: `POST /api/template/parse`
- **功能**: 解析模板，识别文本元素并生成参数候选项
- **请求体**:
  - `templateId` (必填): 模板ID

**请求示例**:
```bash
POST /api/template/parse
Content-Type: application/json

{
  "templateId": "2"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "templateId": "2",
    "templateTitle": "示例模板 - 日签插画手机海报",
    "textElements": [
      {
        "uuid": "98fd9b16db8a",
        "type": "w-text",
        "text": "你好,十二月",
        "position": {
          "left": 84.11,
          "top": 289.4,
          "width": 1092.38,
          "height": 211
        },
        "style": {
          "fontSize": 176,
          "color": "#000000ff",
          "textAlign": "center",
          "lineHeight": 1.2,
          "letterSpacing": 10,
          "fontWeight": 400,
          "fontStyle": "normal"
        }
      }
    ],
    "parameterCandidates": [
      {
        "elementUuid": "98fd9b16db8a",
        "suggestedName": "greeting",
        "suggestedLabel": "问候语",
        "suggestedDescription": "可自定义的文本内容",
        "suggestedType": "text",
        "originalText": "你好,十二月",
        "textCategory": "general",
        "maxLength": 100,
        "isRequired": false
      }
    ],
    "summary": {
      "totalTextElements": 6,
      "totalParameterCandidates": 6,
      "categories": {
        "phone": 1,
        "general": 4,
        "url": 1
      }
    }
  }
}
```

### 2.2 生成参数化预览
- **接口**: `POST /api/parameter/preview`
- **功能**: 根据参数数据生成预览
- **请求体**:
  - `templateId` (必填): 模板ID
  - `parameters` (必填): 参数数据对象

**请求示例**:
```bash
POST /api/parameter/preview
Content-Type: application/json

{
  "templateId": "2",
  "parameters": {
    "greeting": "你好,新年快乐",
    "subtitle": "2025年新春祝福",
    "phone": "************"
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "previewUrl": "http://localhost:7001/preview/parameter/preview_123456789",
    "templateId": "2",
    "parameters": {
      "greeting": "你好,新年快乐",
      "subtitle": "2025年新春祝福",
      "phone": "************"
    },
    "generatedAt": "2025-01-16T10:00:00Z"
  }
}
```

### 2.3 执行参数替换
- **接口**: `POST /api/parameter/replace`
- **功能**: 执行参数替换，返回替换后的模板数据
- **请求体**:
  - `templateId` (必填): 模板ID
  - `parameters` (必填): 参数数据对象

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "templateData": "替换后的模板JSON数据",
    "replacedElements": 6,
    "errors": [],
    "metadata": {
      "templateId": "2",
      "processedAt": "2025-01-16T10:00:00Z",
      "replacements": [
        {
          "elementUuid": "98fd9b16db8a",
          "originalText": "你好,十二月",
          "newText": "你好,新年快乐",
          "parameterName": "greeting"
        }
      ]
    }
  }
}
```

### 2.4 批量生成图片
- **接口**: `POST /api/parameter/batch-generate`
- **功能**: 批量生成参数化图片
- **请求体**:
  - `templateId` (必填): 模板ID
  - `parametersList` (必填): 参数数据数组
  - `outputOptions` (必填): 输出选项

**请求示例**:
```bash
POST /api/parameter/batch-generate
Content-Type: application/json

{
  "templateId": "2",
  "parametersList": [
    {
      "greeting": "你好,新年快乐",
      "subtitle": "2025年新春祝福"
    },
    {
      "greeting": "恭喜发财",
      "subtitle": "财源滚滚来"
    }
  ],
  "outputOptions": {
    "width": 1242,
    "height": 2208,
    "type": "file",
    "quality": 0.9
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "batchId": "batch_1755432640277_xxx",
    "status": "processing",
    "totalItems": 2,
    "message": "批量任务已创建，正在处理中"
  }
}
```

### 2.5 查询批量任务状态
- **接口**: `GET /api/parameter/batch-status/:batchId`
- **功能**: 查询批量生成任务的状态和进度
- **参数**:
  - `batchId` (路径参数): 批量任务ID

**请求示例**:
```bash
GET /api/parameter/batch-status/batch_1755432640277_xxx
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "batchId": "batch_1755432640277_xxx",
    "status": "completed",
    "progress": 100,
    "totalItems": 2,
    "completedItems": 2,
    "failedItems": 0,
    "createdAt": "2025-01-16T10:00:00Z",
    "updatedAt": "2025-01-16T10:05:00Z",
    "results": [
      {
        "index": 0,
        "status": "success",
        "imageUrl": "http://localhost:7001/static/generated/batch_item_0_1242x2208.jpg",
        "parameters": {
          "greeting": "你好,新年快乐",
          "subtitle": "2025年新春祝福"
        }
      },
      {
        "index": 1,
        "status": "success",
        "imageUrl": "http://localhost:7001/static/generated/batch_item_1_1242x2208.jpg",
        "parameters": {
          "greeting": "恭喜发财",
          "subtitle": "财源滚滚来"
        }
      }
    ],
    "outputOptions": {
      "width": 1242,
      "height": 2208,
      "type": "file",
      "quality": 0.9
    }
  }
}
```

## 3. 截图服务 API

### 3.1 设计稿截图
- **接口**: `GET /api/screenshots`
- **功能**: 对设计稿进行截图，支持模板和组件截图
- **参数**:
  - `id` (可选): 截图ID，高优先级
  - `tempid` (可选): 模板ID，低优先级，无id时取该值
  - `parameterDataId` (可选): 参数数据ID，用于参数化截图
  - `width` (必填): 视窗宽度
  - `height` (必填): 视窗高度
  - `screenshot_url` (可选): 自定义截图页面URL
  - `type` (可选): file=正常截图返回，cover=封面生成，默认file
  - `size` (可选): 按比例缩小到指定宽度
  - `quality` (可选): 图片质量，0.1-1.0，默认0.8
  - `index` (可选): 下载哪个画板，默认0

**请求示例**:
```bash
GET /api/screenshots?id=2&width=1242&height=2208&quality=0.9
```

**响应**: 返回图片二进制数据 (Content-Type: image/jpeg)

### 3.2 网页全屏截图
- **接口**: `GET /api/printscreen`
- **功能**: 对任意网页进行全屏截图
- **参数**:
  - `url` (必填): 目标网页链接
  - `width` (可选): 视窗宽度，默认375
  - `height` (可选): 视窗高度，默认0
  - `prevent` (可选): 是否阻止立即截图，默认false
  - `type` (可选): file=返回二进制文件，cover=返回地址，默认file
  - `size` (可选): 等比缩放到指定宽度（仅type=cover生效）
  - `quality` (可选): 压缩质量1-100（仅有size时生效）
  - `wait` (可选): 截图前等待时间，单位ms
  - `ua` (可选): 模拟设备User-Agent
  - `devices` (可选): 套用设备预设，如"iPhone 6"
  - `scale` (可选): 设备像素比(DPR)，范围1-4，默认1

**请求示例**:
```bash
GET /api/printscreen?url=https://example.com&width=1200&height=800&type=cover
```

**响应示例** (type=cover时):
```json
{
  "code": 200,
  "message": "截图成功",
  "data": {
    "path": "/cache/screenshot_1234567890_uuid.png",
    "thumbPath": "/cache/screenshot_1234567890_uuid.jpg",
    "url": "http://localhost:7001/static/cache/screenshot_1234567890_uuid.png"
  }
}
```

## 4. 文件管理 API

### 4.1 文件上传
- **接口**: `POST /api/file/upload`
- **功能**: 上传文件到服务器
- **请求参数** (multipart/form-data):
  - `file` (必填): 二进制文件
  - `folder` (可选): 目标文件夹，空为根目录
  - `name` (可选): 文件名，默认随机生成

**请求示例**:
```bash
POST /api/file/upload
Content-Type: multipart/form-data

file: [binary data]
folder: user
name: my-image.jpg
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "key": "user/my-image.jpg",
    "url": "http://localhost:7001/static/user/my-image.jpg",
    "size": 1024000,
    "uploadedAt": "2025-01-16T10:00:00Z"
  }
}
```

### 4.2 获取用户文件列表
- **接口**: `GET /api/user/files`
- **功能**: 获取用户上传的文件列表
- **参数**:
  - `folder` (可选): 文件夹筛选
  - `type` (可选): 文件类型筛选，如image、video等
  - `page` (可选): 页码，默认1
  - `pageSize` (可选): 每页数量，默认20

**请求示例**:
```bash
GET /api/user/files?folder=user&type=image&page=1&pageSize=20
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "name": "my-image.jpg",
        "url": "http://localhost:7001/static/user/my-image.jpg",
        "size": 1024000,
        "type": "image/jpeg",
        "uploadedAt": "2025-01-16T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20
  }
}
```

## 5. 素材管理 API

### 5.1 获取素材列表
- **接口**: `GET /api/materials`
- **功能**: 获取素材列表
- **参数**:
  - `category` (必填): 素材分类
  - `page` (可选): 页码，默认1
  - `pageSize` (可选): 每页数量，默认20
  - `keyword` (可选): 关键词搜索

**请求示例**:
```bash
GET /api/materials?category=icons&page=1&pageSize=20
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "图标素材1",
        "url": "http://localhost:7001/static/materials/icon1.svg",
        "thumbnail": "http://localhost:7001/static/materials/icon1_thumb.jpg",
        "category": "icons",
        "tags": ["商务", "办公"],
        "format": "svg"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}
```

### 5.2 获取照片素材
- **接口**: `GET /api/photos`
- **功能**: 获取照片素材列表
- **参数**:
  - `category` (必填): 照片分类
  - `page` (可选): 页码，默认1
  - `pageSize` (可选): 每页数量，默认20
  - `keyword` (可选): 关键词搜索

**请求示例**:
```bash
GET /api/photos?category=business&page=1&pageSize=20
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "商务照片1",
        "url": "http://localhost:7001/static/photos/business1.jpg",
        "thumbnail": "http://localhost:7001/static/photos/business1_thumb.jpg",
        "category": "business",
        "tags": ["办公", "会议"],
        "width": 1920,
        "height": 1080
      }
    ],
    "total": 200,
    "page": 1,
    "pageSize": 20
  }
}
```

## 6. 系统管理 API

### 6.1 系统健康检查
- **接口**: `GET /health`
- **功能**: 检查系统整体健康状态

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "healthy",
    "timestamp": "2025-01-16T10:00:00Z",
    "services": {
      "database": "healthy",
      "cache": "healthy"
    },
    "version": "1.0.0"
  }
}
```

### 6.2 系统信息查询
- **接口**: `GET /api/health/info`
- **功能**: 获取详细的系统信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "service": "poster-design-api",
    "version": "1.0.0",
    "environment": "development",
    "port": 7001,
    "nodeVersion": "v18.17.0",
    "platform": "win32",
    "arch": "x64",
    "uptime": 3600.123,
    "memory": {
      "rss": 52428800,
      "heapTotal": 41943040,
      "heapUsed": 29876224,
      "external": 1089536
    },
    "timestamp": "2025-01-16T10:00:00Z"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，token无效 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 使用限制

- **API请求频率限制**: 每秒最多100次请求
- **批量生成限制**: 每次最多处理50个项目
- **图片生成限制**: 最大尺寸4000x4000像素
- **文件上传限制**: 单文件最大50MB
- **缓存时间**: 模板数据缓存10分钟，用户数据缓存5分钟

## 认证说明

### Bearer Token认证
所有API请求需要在Header中包含认证信息：

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 获取访问令牌
请联系系统管理员获取访问令牌，或通过用户登录接口获取。

## SDK和集成示例

### JavaScript/TypeScript SDK

```typescript
class PosterDesignAPI {
  private baseURL: string
  private token: string

  constructor(baseURL: string, token: string) {
    this.baseURL = baseURL
    this.token = token
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`
    const headers = {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json',
      ...options.headers
    }

    const response = await fetch(url, { ...options, headers })
    const result = await response.json()

    if (result.code !== 200) {
      throw new Error(result.message || '请求失败')
    }

    return result.data
  }

  // 模板管理
  async getTemplates(params?: {
    page?: number
    pageSize?: number
    category?: string
    keyword?: string
  }) {
    const query = new URLSearchParams(params as any).toString()
    return this.request(`/api/templates?${query}`)
  }

  async getTemplate(id: string) {
    return this.request(`/api/template?id=${id}`)
  }

  async saveTemplate(templateData: any) {
    return this.request('/api/template/save', {
      method: 'POST',
      body: JSON.stringify(templateData)
    })
  }

  // 动态参数功能
  async parseTemplate(templateId: string) {
    return this.request('/api/template/parse', {
      method: 'POST',
      body: JSON.stringify({ templateId })
    })
  }

  async generatePreview(templateId: string, parameters: any) {
    return this.request('/api/parameter/preview', {
      method: 'POST',
      body: JSON.stringify({ templateId, parameters })
    })
  }

  async batchGenerate(templateId: string, parametersList: any[], outputOptions: any) {
    return this.request('/api/parameter/batch-generate', {
      method: 'POST',
      body: JSON.stringify({ templateId, parametersList, outputOptions })
    })
  }

  async getBatchStatus(batchId: string) {
    return this.request(`/api/parameter/batch-status/${batchId}`)
  }

  // 截图服务
  getScreenshotUrl(params: {
    id?: string
    tempid?: string
    parameterDataId?: string
    width: number
    height: number
    quality?: number
    type?: string
  }) {
    const query = new URLSearchParams(params as any).toString()
    return `${this.baseURL}/api/screenshots?${query}`
  }

  // 文件上传
  async uploadFile(file: File, folder?: string, name?: string) {
    const formData = new FormData()
    formData.append('file', file)
    if (folder) formData.append('folder', folder)
    if (name) formData.append('name', name)

    return this.request('/api/file/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
      body: formData
    })
  }
}

// 使用示例
const api = new PosterDesignAPI('http://localhost:7001', 'your-access-token')

// 获取模板列表
const templates = await api.getTemplates({ category: 'poster', pageSize: 10 })

// 解析模板参数
const parseResult = await api.parseTemplate('2')

// 批量生成图片
const batchResult = await api.batchGenerate('2', [
  { greeting: '你好,新年快乐', subtitle: '2025年新春祝福' },
  { greeting: '恭喜发财', subtitle: '财源滚滚来' }
], {
  width: 1242,
  height: 2208,
  quality: 0.9
})

// 查询批量任务状态
const status = await api.getBatchStatus(batchResult.batchId)
```

### Vue.js 组件示例

```vue
<template>
  <div class="poster-design-integration">
    <!-- 模板选择器 -->
    <TemplateSelector
      :templates="templates"
      @select="handleTemplateSelect"
    />

    <!-- 参数配置器 -->
    <ParameterEditor
      v-if="selectedTemplate && parameterCandidates.length"
      :candidates="parameterCandidates"
      :values="parameterValues"
      @update="handleParameterUpdate"
    />

    <!-- 预览区域 -->
    <PreviewArea
      v-if="previewUrl"
      :url="previewUrl"
      @generate="handleGenerate"
    />

    <!-- 批量生成 -->
    <BatchGenerator
      v-if="selectedTemplate"
      :template-id="selectedTemplate.id"
      :parameter-candidates="parameterCandidates"
      @batch-complete="handleBatchComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { PosterDesignAPI } from './api'

const api = new PosterDesignAPI('http://localhost:7001', 'your-token')

const templates = ref([])
const selectedTemplate = ref(null)
const parameterCandidates = ref([])
const parameterValues = ref({})
const previewUrl = ref('')

onMounted(async () => {
  const result = await api.getTemplates()
  templates.value = result.list
})

async function handleTemplateSelect(template: any) {
  selectedTemplate.value = template
  const parseResult = await api.parseTemplate(template.id)
  parameterCandidates.value = parseResult.parameterCandidates
  
  // 初始化参数值
  parameterValues.value = {}
  parameterCandidates.value.forEach(param => {
    parameterValues.value[param.suggestedName] = param.originalText
  })
}

async function handleParameterUpdate(values: any) {
  parameterValues.value = values
  const result = await api.generatePreview(selectedTemplate.value.id, values)
  previewUrl.value = result.previewUrl
}

function handleGenerate() {
  const url = api.getScreenshotUrl({
    id: selectedTemplate.value.id,
    width: selectedTemplate.value.width,
    height: selectedTemplate.value.height,
    quality: 0.9
  })
  window.open(url, '_blank')
}

function handleBatchComplete(results: any) {
  console.log('批量生成完成:', results)
}
</script>
```

## 注意事项

1. **环境配置**: 本文档基于开发环境，生产环境需要相应调整URL和配置
2. **认证安全**: 请妥善保管访问令牌，避免泄露
3. **文件路径**: 上传文件保存在 `/static/` 目录下，可通过HTTP直接访问
4. **截图服务**: 依赖Puppeteer，需要Chrome浏览器环境
5. **队列限制**: 截图和批量生成服务有队列限制，避免服务器过载
6. **缓存机制**: 系统使用缓存提高性能，可通过缓存管理API进行控制
7. **错误处理**: 建议在客户端实现重试机制和错误处理逻辑

## 更新日志

- **v1.0.0** (2025-01-16): 初始版本，包含完整的API功能
- 支持基础模板管理和设计功能
- 支持动态参数模板系统
- 支持截图和文件管理服务
- 支持批量图片生成功能

---

**文档版本**: v1.0.0  
**最后更新**: 2025-01-16  
**技术支持**: 请联系开发团队获取技术支持